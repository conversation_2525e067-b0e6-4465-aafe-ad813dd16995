"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import {
	MessageSquare,
	ArrowLeft,
	User,
	Bell,
	Shield,
	Trash2,
	Download,
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { motion } from "framer-motion";
import { createClient } from "@/lib/client";

export default function SettingsPage() {
	const [user, setUser] = useState<any>(null);
	const [loading, setLoading] = useState(true);
	const [emailNotifications, setEmailNotifications] = useState(true);
	const [pushNotifications, setPushNotifications] = useState(false);
	const [dataProcessing, setDataProcessing] = useState(true);
	const supabase = createClient();
	const router = useRouter();
	const { toast } = useToast();

	useEffect(() => {
		const getUser = async () => {
			const {
				data: { session },
			} = await supabase.auth.getSession();
			if (!session) {
				router.push("/auth/login");
				return;
			}
			setUser(session.user);
			setLoading(false);
		};
		getUser();
	}, [supabase, router]);

	const handleDeleteAccount = async () => {
		if (
			confirm(
				"Are you sure you want to delete your account? This action cannot be undone."
			)
		) {
			try {
				// In a real app, you'd call a server action to delete the user
				toast({
					title: "Account Deletion",
					description: "Please contact support to delete your account.",
					open: true,
				});
			} catch (error) {
				toast({
					title: "Error",
					description: "Failed to delete account",
					variant: "destructive",
					open: true,
				});
			}
		}
	};

	const handleExportData = async () => {
		try {
			// In a real app, you'd export user data
			toast({
				title: "Data Export",
				description: "Your data export will be emailed to you within 24 hours.",
				open: true,
			});
		} catch (error) {
			toast({
				title: "Error",
				description: "Failed to export data",
				variant: "destructive",
				open: true,
			});
		}
	};

	if (loading) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<MessageSquare className="h-12 w-12 text-blue-600 mx-auto mb-4" />
					<p className="text-gray-600">Loading...</p>
				</div>
			</div>
		);
	}

	return (
		<div className="bg-gray-50">
			<div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.6 }}
				>
					<div className="mb-8">
						<h1 className="text-3xl font-bold text-gray-900">Settings</h1>
						<p className="text-gray-600 mt-2">
							Manage your account preferences and privacy settings
						</p>
					</div>

					<div className="space-y-6">
						{/* Account Settings */}
						<Card>
							<CardHeader>
								<CardTitle className="flex items-center">
									<User className="h-5 w-5 mr-2" />
									Account Settings
								</CardTitle>
								<CardDescription>
									Manage your account information and preferences
								</CardDescription>
							</CardHeader>
							<CardContent className="space-y-4">
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<div className="space-y-2">
										<Label htmlFor="email">Email Address</Label>
										<Input
											id="email"
											type="email"
											value={user?.email || ""}
											disabled
										/>
									</div>
									<div className="space-y-2">
										<Label htmlFor="plan">Current Plan</Label>
										<Input id="plan" value="Free Plan" disabled />
									</div>
								</div>
								<div className="flex justify-between items-center pt-4">
									<div>
										<p className="font-medium">Profile Settings</p>
										<p className="text-sm text-gray-600">
											Manage your profile information
										</p>
									</div>
									<Link href="/profile">
										<Button variant="outline">Edit Profile</Button>
									</Link>
								</div>
							</CardContent>
						</Card>

						{/* Notification Settings */}
						<Card>
							<CardHeader>
								<CardTitle className="flex items-center">
									<Bell className="h-5 w-5 mr-2" />
									Notifications
								</CardTitle>
								<CardDescription>
									Configure how you receive notifications
								</CardDescription>
							</CardHeader>
							<CardContent className="space-y-4">
								<div className="flex items-center justify-between">
									<div className="space-y-0.5">
										<Label>Email Notifications</Label>
										<p className="text-sm text-gray-600">
											Receive updates and announcements via email
										</p>
									</div>
									<Switch
										checked={emailNotifications}
										onCheckedChange={setEmailNotifications}
									/>
								</div>
								<Separator />
								<div className="flex items-center justify-between">
									<div className="space-y-0.5">
										<Label>Push Notifications</Label>
										<p className="text-sm text-gray-600">
											Receive push notifications in your browser
										</p>
									</div>
									<Switch
										checked={pushNotifications}
										onCheckedChange={setPushNotifications}
									/>
								</div>
							</CardContent>
						</Card>

						{/* Privacy Settings */}
						<Card>
							<CardHeader>
								<CardTitle className="flex items-center">
									<Shield className="h-5 w-5 mr-2" />
									Privacy & Security
								</CardTitle>
								<CardDescription>
									Control your privacy and data usage preferences
								</CardDescription>
							</CardHeader>
							<CardContent className="space-y-4">
								<div className="flex items-center justify-between">
									<div className="space-y-0.5">
										<Label>Data Processing</Label>
										<p className="text-sm text-gray-600">
											Allow us to process your data to improve our services
										</p>
									</div>
									<Switch
										checked={dataProcessing}
										onCheckedChange={setDataProcessing}
									/>
								</div>
								<Separator />
								<div className="space-y-4">
									<div>
										<h4 className="font-medium mb-2">Data Management</h4>
										<div className="flex flex-col sm:flex-row gap-2">
											<Button
												variant="outline"
												onClick={handleExportData}
												className="flex items-center bg-transparent"
											>
												<Download className="h-4 w-4 mr-2" />
												Export My Data
											</Button>
										</div>
									</div>
								</div>
							</CardContent>
						</Card>

						{/* Danger Zone */}
						<Card className="border-red-200">
							<CardHeader>
								<CardTitle className="flex items-center text-red-600">
									<Trash2 className="h-5 w-5 mr-2" />
									Danger Zone
								</CardTitle>
								<CardDescription>
									Irreversible and destructive actions
								</CardDescription>
							</CardHeader>
							<CardContent>
								<div className="flex items-center justify-between p-4 border border-red-200 rounded-lg bg-red-50">
									<div>
										<p className="font-medium text-red-900">Delete Account</p>
										<p className="text-sm text-red-700">
											Permanently delete your account and all associated data.
											This action cannot be undone.
										</p>
									</div>
									<Button variant="destructive" onClick={handleDeleteAccount}>
										Delete Account
									</Button>
								</div>
							</CardContent>
						</Card>
					</div>
				</motion.div>
			</div>
		</div>
	);
}
