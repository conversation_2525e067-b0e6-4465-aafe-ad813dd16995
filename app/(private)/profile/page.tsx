"use client";

import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
	MessageSquare,
	ArrowLeft,
	User,
	Mail,
	Calendar,
	Save,
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { motion } from "framer-motion";
import { createClient } from "@/lib/client";
import { useUser } from "@/contexts/user-context";

export default function ProfilePage() {
	const [loading, setLoading] = useState(true);
	const [saving, setSaving] = useState(false);
	const [profile, setProfile] = useState({
		full_name: "",
		bio: "",
		location: "",
		website: "",
	});

	const { user } = useUser();
	const supabase = createClient();
	const router = useRouter();
	const { toast } = useToast();

	useEffect(() => {
		const getUser = async () => {
			if (!user?.id) return;

			// Load user profile if it exists
			const { data: profileData } = await supabase
				.from("profiles")
				.select("*")
				.eq("id", user.id)
				.single();

			if (profileData) {
				setProfile({
					bio: profileData.bio ?? "",
					full_name: profileData.full_name ?? "",
					location: profileData.location ?? "",
					website: profileData.website ?? "",
				});
			}

			setLoading(false);
		};
		getUser();
	}, [supabase]);

	const handleSave = async () => {
		if (!user) return;

		setSaving(true);
		try {
			const { error } = await supabase.from("profiles").upsert({
				id: user.id,
				...profile,
				updated_at: new Date().toISOString(),
			});

			if (error) throw error;

			toast({
				title: "Success",
				description: "Profile updated successfully!",
				open: true,
			});
		} catch (error) {
			toast({
				title: "Error",
				description: "Failed to update profile",
				variant: "destructive",
				open: true,
			});
		} finally {
			setSaving(false);
		}
	};

	if (loading) {
		return (
			<div className="h-full bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<MessageSquare className="h-12 w-12 text-blue-600 mx-auto mb-4" />
					<p className="text-gray-600">Loading...</p>
				</div>
			</div>
		);
	}

	return (
		<div className="h-full bg-gray-50">
			<div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
				<motion.div
					initial={{ opacity: 0, y: 20 }}
					animate={{ opacity: 1, y: 0 }}
					transition={{ duration: 0.6 }}
				>
					<div className="mb-8">
						<h1 className="text-3xl font-bold text-gray-900">Profile</h1>
						<p className="text-gray-600 mt-2">
							Manage your personal information and preferences
						</p>
					</div>

					<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
						{/* Profile Overview */}
						<Card className="lg:col-span-1">
							<CardHeader className="text-center">
								<Avatar className="w-24 h-24 mx-auto mb-4">
									<AvatarFallback className="text-2xl">
										{user?.email?.charAt(0).toUpperCase()}
									</AvatarFallback>
								</Avatar>
								<CardTitle>{profile.full_name || "Anonymous User"}</CardTitle>
								<CardDescription>{user?.email}</CardDescription>
							</CardHeader>
							<CardContent className="space-y-4">
								<div className="flex items-center text-sm text-gray-600">
									<Mail className="h-4 w-4 mr-2" />
									{user?.email}
								</div>
								{user?.created_at && (
									<div className="flex items-center text-sm text-gray-600">
										<Calendar className="h-4 w-4 mr-2" />
										Joined {new Date(user.created_at).toLocaleDateString()}
									</div>
								)}
								{profile.location && (
									<div className="flex items-center text-sm text-gray-600">
										<User className="h-4 w-4 mr-2" />
										{profile.location}
									</div>
								)}
							</CardContent>
						</Card>

						{/* Profile Form */}
						<Card className="lg:col-span-2">
							<CardHeader>
								<CardTitle>Personal Information</CardTitle>
								<CardDescription>
									Update your personal details and preferences
								</CardDescription>
							</CardHeader>
							<CardContent className="space-y-6">
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<div className="space-y-2">
										<Label htmlFor="full_name">Full Name</Label>
										<Input
											id="full_name"
											value={profile.full_name}
											onChange={(e) =>
												setProfile({ ...profile, full_name: e.target.value })
											}
											placeholder="Enter your full name"
										/>
									</div>
									<div className="space-y-2">
										<Label htmlFor="location">Location</Label>
										<Input
											id="location"
											value={profile.location}
											onChange={(e) =>
												setProfile({ ...profile, location: e.target.value })
											}
											placeholder="City, Country"
										/>
									</div>
								</div>

								<div className="space-y-2">
									<Label htmlFor="website">Website</Label>
									<Input
										id="website"
										type="url"
										value={profile.website}
										onChange={(e) =>
											setProfile({ ...profile, website: e.target.value })
										}
										placeholder="https://yourwebsite.com"
									/>
								</div>

								<div className="space-y-2">
									<Label htmlFor="bio">Bio</Label>
									<Textarea
										id="bio"
										value={profile.bio}
										onChange={(e) =>
											setProfile({ ...profile, bio: e.target.value })
										}
										placeholder="Tell us about yourself..."
										rows={4}
									/>
								</div>

								<div className="flex justify-end">
									<Button onClick={handleSave} disabled={saving}>
										<Save className="h-4 w-4 mr-2" />
										{saving ? "Saving..." : "Save Changes"}
									</Button>
								</div>
							</CardContent>
						</Card>
					</div>

					{/* Account Statistics */}
					<Card className="mt-6">
						<CardHeader>
							<CardTitle>Account Statistics</CardTitle>
							<CardDescription>
								Your activity and usage statistics
							</CardDescription>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
								<div className="text-center">
									<div className="text-2xl font-bold text-blue-600">0</div>
									<div className="text-sm text-gray-600">Conversations</div>
								</div>
								<div className="text-center">
									<div className="text-2xl font-bold text-green-600">0</div>
									<div className="text-sm text-gray-600">Messages Sent</div>
								</div>
								<div className="text-center">
									<div className="text-2xl font-bold text-purple-600">Free</div>
									<div className="text-sm text-gray-600">Current Plan</div>
								</div>
							</div>
						</CardContent>
					</Card>
				</motion.div>
			</div>
		</div>
	);
}
