"use client";

import { useEffect, useState } from "react";
import { useChat } from "ai/react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import {
	MessageSquare,
	Send,
	User,
	Bot,
} from "lucide-react";
import { useRouter } from "next/navigation";
import ReactMarkdown from "react-markdown";
import { motion, AnimatePresence } from "framer-motion";
import { createClient } from "@/lib/client";
import { useUser } from "@/contexts/user-context";

export default function ChatPage() {
	const [currentConversationId, setCurrentConversationId] = useState<
		string | null
	>(null);

	const { user } = useUser();
	const { messages, input, handleInputChange, handleSubmit, isLoading } =
		useChat();
	const supabase = createClient();
	const router = useRouter();

	const saveConversation = async (messages: any[]) => {
		if (!user || messages.length === 0) return;

		try {
			let conversationId = null;

			if (!conversationId) {
				// Create new conversation
				const title = messages[0]?.content?.slice(0, 50) + "..." || "New Chat";
				const { data: conversation, error: convError } = await supabase
					.from("conversations")
					.insert({
						user_id: user.id,
						title,
					})
					.select()
					.single();

				if (convError) throw convError;
				conversationId = conversation.id;
				setCurrentConversationId(conversationId);
			}

			// Save messages
			const messagesToSave = messages.map((msg) => ({
				conversation_id: conversationId,
				role: msg.role,
				content: msg.content,
			}));

			await supabase
				.from("messages")
				.delete()
				.eq("conversation_id", conversationId);
			const { error: msgError } = await supabase
				.from("messages")
				.insert(messagesToSave);

			if (msgError) throw msgError;

			// Update conversation timestamp
			await supabase
				.from("conversations")
				.update({ updated_at: new Date().toISOString() })
				.eq("id", conversationId);
		} catch (error) {
			console.error("Error saving conversation:", error);
		}
	};

	useEffect(() => {
		if (messages.length > 0) {
			saveConversation(messages);
		}
	}, [messages]);

	if (!user) {
		return (
			<div className="min-h-screen bg-gray-50 flex items-center justify-center">
				<div className="text-center">
					<MessageSquare className="h-12 w-12 text-blue-600 mx-auto mb-4" />
					<p className="text-gray-600">Loading...</p>
				</div>
			</div>
		);
	}

	return (
		<div className="flex h-screen bg-gray-50">
			{/* Main Chat Area */}
			<div className="flex-1 flex flex-col">
				{/* Messages */}
				<ScrollArea className="flex-1 p-4">
					<div className="max-w-4xl mx-auto space-y-6">
						{messages.length === 0 ? (
							<motion.div
								initial={{ opacity: 0, y: 20 }}
								animate={{ opacity: 1, y: 0 }}
								className="text-center py-12"
							>
								<MessageSquare className="h-16 w-16 text-blue-600 mx-auto mb-4" />
								<h2 className="text-2xl font-semibold text-gray-900 mb-2">
									Welcome to ProjectPersona
								</h2>
								<p className="text-gray-600 mb-6">
									Start a conversation with your AI assistant. Ask questions,
									get help, or just chat!
								</p>
								<div className="grid md:grid-cols-2 gap-4 max-w-2xl mx-auto">
									<Card className="p-4 cursor-pointer hover:shadow-md transition-shadow">
										<h3 className="font-medium mb-2">💡 Get Ideas</h3>
										<p className="text-sm text-gray-600">
											Brainstorm creative solutions and innovative concepts
										</p>
									</Card>
									<Card className="p-4 cursor-pointer hover:shadow-md transition-shadow">
										<h3 className="font-medium mb-2">📝 Write Content</h3>
										<p className="text-sm text-gray-600">
											Create articles, emails, and other written content
										</p>
									</Card>
									<Card className="p-4 cursor-pointer hover:shadow-md transition-shadow">
										<h3 className="font-medium mb-2">🔍 Learn & Research</h3>
										<p className="text-sm text-gray-600">
											Get explanations and research assistance on any topic
										</p>
									</Card>
									<Card className="p-4 cursor-pointer hover:shadow-md transition-shadow">
										<h3 className="font-medium mb-2">🛠️ Problem Solving</h3>
										<p className="text-sm text-gray-600">
											Work through challenges and find practical solutions
										</p>
									</Card>
								</div>
							</motion.div>
						) : (
							<AnimatePresence>
								{messages.map((message, index) => (
									<motion.div
										key={message.id}
										initial={{ opacity: 0, y: 20 }}
										animate={{ opacity: 1, y: 0 }}
										transition={{ delay: index * 0.1 }}
										className={`flex items-start space-x-3 ${
											message.role === "user" ? "justify-end" : "justify-start"
										}`}
									>
										{message.role === "assistant" && (
											<Avatar className="mt-1">
												<AvatarFallback className="bg-blue-600 text-white">
													<Bot className="h-4 w-4" />
												</AvatarFallback>
											</Avatar>
										)}
										<div
											className={`max-w-3xl rounded-lg p-4 ${
												message.role === "user"
													? "bg-blue-600 text-white ml-12"
													: "bg-white border border-gray-200"
											}`}
										>
											{message.role === "user" ? (
												<p className="whitespace-pre-wrap">{message.content}</p>
											) : (
												<div className="prose prose-sm max-w-none">
													<ReactMarkdown>{message.content}</ReactMarkdown>
												</div>
											)}
										</div>
										{message.role === "user" && (
											<Avatar className="mt-1">
												<AvatarFallback>
													<User className="h-4 w-4" />
												</AvatarFallback>
											</Avatar>
										)}
									</motion.div>
								))}
							</AnimatePresence>
						)}

						{isLoading && (
							<motion.div
								initial={{ opacity: 0 }}
								animate={{ opacity: 1 }}
								className="flex items-start space-x-3"
							>
								<Avatar>
									<AvatarFallback className="bg-blue-600 text-white">
										<Bot className="h-4 w-4" />
									</AvatarFallback>
								</Avatar>
								<div className="bg-white border border-gray-200 rounded-lg p-4">
									<div className="flex space-x-1">
										<div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
										<div
											className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
											style={{ animationDelay: "0.1s" }}
										></div>
										<div
											className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
											style={{ animationDelay: "0.2s" }}
										></div>
									</div>
								</div>
							</motion.div>
						)}
					</div>
				</ScrollArea>

				{/* Input */}
				<div className="border-t bg-white p-4">
					<div className="max-w-4xl mx-auto">
						<form onSubmit={handleSubmit} className="flex space-x-3">
							<Input
								value={input}
								onChange={handleInputChange}
								placeholder="Type your message..."
								className="flex-1"
								disabled={isLoading}
							/>
							<Button type="submit" disabled={isLoading || !input.trim()}>
								<Send className="h-4 w-4" />
							</Button>
						</form>
						<p className="text-xs text-gray-500 text-center mt-2">
							ProjectPersona can make mistakes. Consider checking important
							information.
						</p>
					</div>
				</div>
			</div>
		</div>
	);
}
