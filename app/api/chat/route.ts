import { createClient } from "@/lib/server";
import { openai } from "@ai-sdk/openai";
import { streamText } from "ai";
import { cookies } from "next/headers";

export const maxDuration = 30;

export async function POST(req: Request) {
	try {
		// Verify user authentication
		const cookieStore = await cookies();
		const supabase = await createClient();

		const {
			data: { session },
		} = await supabase.auth.getSession();

		if (!session) {
			return new Response("Unauthorized", { status: 401 });
		}

		const { messages } = await req.json();

		// Stream the AI response
		const result = streamText({
			model: openai("gpt-4o"),
			messages,
			system: `You are <PERSON><PERSON><PERSON><PERSON>, a helpful and intelligent AI assistant. You provide thoughtful, accurate, and engaging responses while maintaining a friendly and professional tone. Always aim to be helpful while being concise and clear in your explanations.`,
		});

		return result.toDataStreamResponse();
	} catch (error) {
		console.error("Chat API error:", error);
		return new Response("Internal Server Error", { status: 500 });
	}
}
