// import { createMiddlewareClient } from "@supabase/auth-helpers-nextjs"
// import { NextResponse } from "next/server"
// import type { NextRequest } from "next/server"

// export async function middleware(req: NextRequest) {
//   const res = NextResponse.next()
//   const supabase = createMiddlewareClient({ req, res })

//   const {
//     data: { session },
//   } = await supabase.auth.getSession()

//   // Protect the /chat route
//   if (req.nextUrl.pathname.startsWith("/chat") && !session) {
//     return NextResponse.redirect(new URL("/auth/login", req.url))
//   }

//   // Redirect authenticated users away from auth pages
//   if ((req.nextUrl.pathname.startsWith("/auth/login") || req.nextUrl.pathname.startsWith("/auth/signup")) && session) {
//     return NextResponse.redirect(new URL("/chat", req.url))
//   }

//   return res
// }

// export const config = {
//   matcher: ["/chat/:path*", "/auth/:path*"],
// }

import { type NextRequest } from "next/server";
import { updateSession } from "./lib/utils/middleware";

export async function middleware(request: NextRequest) {
	return await updateSession(request);
}

export const config = {
	matcher: [
		/*
		 * Match all request paths except for the ones starting with:
		 * - _next/static (static files)
		 * - _next/image (image optimization files)
		 * - favicon.ico (favicon file)
		 * Feel free to modify this pattern to include more paths.
		 */
		"/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
	],
};
