"use client";

import { createContext, useContext, useEffect, useState } from "react";
import { Loader2 } from "lucide-react";

import { Session } from "@supabase/supabase-js";
import { User } from "@/types";
import { createClient } from "@/lib/client";
import { usePathname, useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";

export const SessionContext = createContext<{
	session: Session | null;
	user: User | null;
	initializeSession: () => Promise<void>;
}>({
	session: null,
	user: null,
	initializeSession: async () => {},
});

interface SessionProviderProps {
	children: React.ReactNode;
}

/**
 * Check if a given pathname corresponds to a public route
 * Public routes are those that should be accessible without authentication
 * Based on the (public) folder structure in the app directory
 */
export function isPublicPath(pathname: string): boolean {
	// Define public routes based on the (public) folder structure
	const publicRoutes = [
		"/", // Landing page (root)
		"/about", // About page
		"/auth/login", // Login page
		"/auth/signup", // Signup page
		"/auth", // Auth base path
	];

	// Check if the pathname matches any public route
	return publicRoutes.some((route) => {
		if (route === "/") {
			return pathname === "/";
		}
		return pathname.startsWith(route);
	});
}

export const SessionProvider = ({ children }: SessionProviderProps) => {
	const [session, setSession] = useState<Session | null>(null);
	const [user, setUser] = useState<User | null>(null);
	const [loading, setLoading] = useState(true);

	const supabase = createClient();
	const { toast } = useToast();
	const router = useRouter();
	const pathname = usePathname();

	const currentIsPublicRoute = isPublicPath(pathname);

	const handleSessionError = () => {
		setSession(null);
		localStorage.clear();
		if (!pathname.includes("/auth")) {
			router.push("/auth/login");
			toast({
				variant: "destructive",
				description: "Your session has expired. Please log in again!",
			});
		}
	};

	const initializeSession = async () => {
		try {
			const {
				data: { session: initialSession },
				error: sessionError,
			} = await supabase.auth.getSession();

			if (sessionError) {
				console.error("Error getting initial session:", sessionError);
				handleSessionError();
				return;
			}

			if (!initialSession) {
				if (!currentIsPublicRoute) {
					const returnUrl = encodeURIComponent(pathname + location.search);
					router.push("/auth/login");
					return;
				}
			} else {
				setSession(initialSession);
				if (pathname.includes("/auth")) {
					router.push("/chat");
				}

				const { data: user } = await supabase
					.from("profiles")
					.select("*")
					.eq("id", initialSession.user.id)
					.single();

				setUser(user);
			}
		} catch (error) {
			console.error("Unexpected error during session initialization:", error);
			handleSessionError();
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		// Initialize session
		initializeSession();
	}, [currentIsPublicRoute]);

	useEffect(() => {
		// Set up auth state change listener
		const {
			data: { subscription },
		} = supabase.auth.onAuthStateChange(async (event, currentSession) => {
			if (event === "SIGNED_OUT") {
				setSession(null);
				localStorage.clear();
				router.push("/auth/login");
			} else if (event === "SIGNED_IN" || event === "TOKEN_REFRESHED") {
				if (currentSession) {
					setSession(currentSession);

					if (currentIsPublicRoute) {
						router.push("/chat");
					}
				}
			}
		});

		return () => {
			subscription.unsubscribe();
		};
	}, [currentIsPublicRoute, pathname]);

	if (loading) {
		return (
			<div className="h-screen w-screen flex items-center justify-center">
				<Loader2 className="h-8 w-8 animate-spin text-primary" />
			</div>
		);
	}

	return (
		<SessionContext.Provider
			value={{
				session,
				user,
				initializeSession,
			}}
		>
			{children}
		</SessionContext.Provider>
	);
};

export const useUser = () => {
	const context = useContext(SessionContext);

	if (!context) {
		throw "useUser must be inside SessionProvider.";
	}

	return context;
};
