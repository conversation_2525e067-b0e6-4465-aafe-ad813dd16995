"use client";

import { AnimatePresence, motion } from "framer-motion";
import {
	LogOut,
	MessageSquare,
	Plus,
	Settings,
	Trash2,
	User,
} from "lucide-react";
import { Button } from "../ui/button";
import { ScrollArea } from "../ui/scroll-area";
import { Card } from "../ui/card";
import { Avatar, AvatarFallback } from "../ui/avatar";
import Link from "next/link";
import { createClient } from "@/lib/client";
import { useToast } from "../ui/use-toast";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useUser } from "@/contexts/user-context";
import { useActiveConversationId } from "@/hooks/atom/conversation-id-atom";

export function Sidebar() {
	const supabase = createClient();
	const { toast } = useToast();
	const router = useRouter();
	const { user } = useUser();

	const [sidebarOpen, setSidebarOpen] = useState(true);
	const [conversations, setConversations] = useState<any[]>([]);
	const [loadingConversations, setLoadingConversations] = useState(true);
	const { activeConversationId, setActiveConversationId } =
		useActiveConversationId();

	const loadConversation = async (conversationId: string) => {
		try {
			router.push(`/chat/${conversationId}`);

			setActiveConversationId(conversationId);
			// You would need to set the messages in the chat hook here
			// This requires modifying the useChat hook or using a custom implementation
		} catch (error) {
			console.error("Error loading conversation:", error);
		}
	};

	const newChat = () => {
		setActiveConversationId(undefined);
		router.push("/chat");
	};

	const deleteConversation = async (conversationId: string) => {
		try {
			const { error } = await supabase
				.from("conversations")
				.delete()
				.eq("id", conversationId);

			if (error) throw error;

			if (activeConversationId === conversationId) {
				setActiveConversationId(undefined);
				newChat();
			}

			toast({
				title: "Conversation deleted",
				description: "The conversation has been removed.",
				open: true,
			});
		} catch (error) {
			console.error("Error deleting conversation:", error);
			toast({
				title: "Error",
				description: "Failed to delete conversation",
				variant: "destructive",
				open: true,
			});
		}
	};

	const loadConversations = async () => {
		if (!user) return;

		try {
			const { data, error } = await supabase
				.from("conversations")
				.select("*")
				.eq("user_id", user.id)
				.order("updated_at", { ascending: false });

			if (error) throw error;
			setConversations(data || []);
		} catch (error) {
			console.error("Error loading conversations:", error);
		} finally {
			setLoadingConversations(false);
		}
	};

	const handleLogout = async () => {
		await supabase.auth.signOut();
		toast({
			title: "Logged out",
			description: "You have been successfully logged out.",
		});
		router.push("/");
	};

	useEffect(() => {
		const getUser = async () => {
			const {
				data: { session },
			} = await supabase.auth.getSession();
			if (!session) {
				router.push("/auth/login");
				return;
			}
		};
		getUser();
	}, [supabase, router]);

	useEffect(() => {
		if (user) {
			loadConversations();
		}
	}, [user]);

	return (
		<AnimatePresence>
			{sidebarOpen && (
				<motion.div
					initial={{ x: -300 }}
					animate={{ x: 0 }}
					exit={{ x: -300 }}
					transition={{ type: "spring", damping: 20 }}
					className="fixed inset-y-0 left-0 z-50 w-80 bg-white border-r border-gray-200 lg:relative lg:translate-x-0 h-screen"
				>
					<div className="flex flex-col h-full">
						{/* Sidebar Header */}
						<div className="flex items-center justify-between p-4 border-b">
							<div className="flex items-center space-x-2">
								<MessageSquare className="h-6 w-6 text-blue-600" />
								<span className="font-semibold text-gray-900">
									ProjectPersona
								</span>
							</div>
						</div>

						{/* New Chat Button */}
						<div className="p-4">
							<Button
								onClick={newChat}
								className="w-full bg-transparent"
								variant="outline"
							>
								<Plus className="h-4 w-4 mr-2" />
								New Chat
							</Button>
						</div>

						{/* Chat History */}
						<ScrollArea className="flex-1 px-4">
							<div className="space-y-2">
								<div className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">
									Recent Chats
								</div>
								{loadingConversations ? (
									<div className="text-sm text-gray-500">Loading...</div>
								) : conversations.length > 0 ? (
									conversations.map((conversation) => (
										<Card
											key={conversation.id}
											className={`group p-3 cursor-pointer hover:bg-gray-50 transition-colors ${
												activeConversationId === conversation.id
													? "bg-blue-50 border-blue-200"
													: ""
											}`}
											onClick={() => loadConversation(conversation.id)}
										>
											<div className="flex items-center justify-between">
												<div className="flex-1 min-w-0">
													<p className="text-sm text-gray-700 truncate">
														{conversation.title}
													</p>
													<p className="text-xs text-gray-500 mt-1">
														{new Date(
															conversation.updated_at
														).toLocaleDateString()}
													</p>
												</div>
												<Button
													variant="ghost"
													size="sm"
													onClick={(e) => {
														e.stopPropagation();
														deleteConversation(conversation.id);
													}}
													className="opacity-0 group-hover:opacity-100 transition-opacity text-destructive hover:text-destructive/80 hover:bg-destructive/10"
												>
													<Trash2 className="h-3 w-3" />
												</Button>
											</div>
										</Card>
									))
								) : (
									<div className="text-sm text-gray-500">
										No conversations yet
									</div>
								)}
							</div>
						</ScrollArea>

						{/* User Menu */}
						<div className="border-t p-4">
							<div className="flex items-center space-x-3 mb-3">
								<Avatar>
									<AvatarFallback>
										{user?.email?.charAt(0).toUpperCase()}
									</AvatarFallback>
								</Avatar>
								<div className="flex-1 min-w-0">
									<p className="text-sm font-medium text-gray-900 truncate">
										{user?.email}
									</p>
									<p className="text-xs text-gray-500">Free Plan</p>
								</div>
							</div>
							<div className="space-y-1">
								<Link href="/profile">
									<Button
										variant="ghost"
										size="sm"
										className="w-full justify-start"
									>
										<User className="h-4 w-4 mr-2" />
										Profile
									</Button>
								</Link>
								<Link href="/settings">
									<Button
										variant="ghost"
										size="sm"
										className="w-full justify-start"
									>
										<Settings className="h-4 w-4 mr-2" />
										Settings
									</Button>
								</Link>
								<Button
									variant="ghost"
									size="sm"
									className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
									onClick={handleLogout}
								>
									<LogOut className="h-4 w-4 mr-2" />
									Logout
								</Button>
							</div>
						</div>
					</div>
				</motion.div>
			)}
		</AnimatePresence>
	);
}
